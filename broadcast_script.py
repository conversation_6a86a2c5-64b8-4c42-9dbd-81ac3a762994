#!/usr/bin/env python3
"""
Standalone Telegram Bot Broadcast Script
This script is completely separate from your existing bot infrastructure.
"""

import asyncio
import logging
import os
import sys
from datetime import datetime
from pathlib import Path
import aiofiles
from telegram import Bo<PERSON>
from telegram.error import TelegramError, RetryAfter, Forbidden, BadRequest

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('broadcast.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BroadcastBot:
    def __init__(self, bot_token):
        self.bot = Bot(token=bot_token)
        self.users_file = "users.txt"
        self.image_path = "broadcast content/image.jpg"
        self.caption_path = "broadcast content/media caption.txt"
        self.log_file = f"broadcast_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        self.test_user_id = 8153676253
        self.batch_size = 30  # 30 users per second
        self.delay_between_batches = 1.0  # 1 second delay
        
    async def load_users(self):
        """Load user IDs from users.txt file"""
        try:
            async with aiofiles.open(self.users_file, 'r') as f:
                content = await f.read()
                # Split by comma and convert to integers
                user_ids = [int(uid.strip()) for uid in content.strip().split(',') if uid.strip()]
                logger.info(f"Loaded {len(user_ids)} user IDs from {self.users_file}")
                return user_ids
        except FileNotFoundError:
            logger.error(f"Users file {self.users_file} not found!")
            return []
        except Exception as e:
            logger.error(f"Error loading users: {e}")
            return []
    
    async def load_caption(self):
        """Load caption from media caption.txt file"""
        try:
            async with aiofiles.open(self.caption_path, 'r', encoding='utf-8') as f:
                caption = await f.read()
                logger.info("Caption loaded successfully")
                return caption.strip()
        except FileNotFoundError:
            logger.error(f"Caption file {self.caption_path} not found!")
            return ""
        except Exception as e:
            logger.error(f"Error loading caption: {e}")
            return ""
    
    async def send_message_to_user(self, user_id, caption, image_path):
        """Send message with image to a single user"""
        try:
            with open(image_path, 'rb') as photo:
                await self.bot.send_photo(
                    chat_id=user_id,
                    photo=photo,
                    caption=caption,
                    parse_mode='HTML'
                )
            return True, None
        except Forbidden:
            return False, "User blocked the bot"
        except BadRequest as e:
            return False, f"Bad request: {e}"
        except RetryAfter as e:
            logger.warning(f"Rate limited for {e.retry_after} seconds")
            await asyncio.sleep(e.retry_after)
            return await self.send_message_to_user(user_id, caption, image_path)
        except TelegramError as e:
            return False, f"Telegram error: {e}"
        except Exception as e:
            return False, f"Unexpected error: {e}"
    
    async def log_result(self, user_id, success, error_msg=None):
        """Log the result of sending message to a user"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        status = "SUCCESS" if success else "FAILED"
        log_entry = f"{timestamp} - User {user_id}: {status}"
        if error_msg:
            log_entry += f" - {error_msg}"
        log_entry += "\n"
        
        async with aiofiles.open(self.log_file, 'a') as f:
            await f.write(log_entry)
    
    async def check_webhook_status(self):
        """Check if webhook is currently set"""
        try:
            webhook_info = await self.bot.get_webhook_info()
            if webhook_info.url:
                print(f"⚠️  WARNING: Webhook is currently active!")
                print(f"   Webhook URL: {webhook_info.url}")
                print(f"   This broadcast script will DISABLE your webhook!")
                print(f"   Your main bot will stop receiving messages during broadcast.")
                return True
            return False
        except Exception as e:
            logger.error(f"Error checking webhook status: {e}")
            return False

    async def send_test_message(self):
        """Send test message to the specified test user"""
        print(f"\n🧪 SENDING TEST MESSAGE TO USER {self.test_user_id}")
        print("=" * 50)
        
        # Load caption
        caption = await self.load_caption()
        if not caption:
            print("❌ Failed to load caption!")
            return False
        
        # Check if image exists
        if not os.path.exists(self.image_path):
            print(f"❌ Image file not found: {self.image_path}")
            return False
        
        # Send test message
        success, error = await self.send_message_to_user(self.test_user_id, caption, self.image_path)
        
        if success:
            print("✅ Test message sent successfully!")
            await self.log_result(self.test_user_id, True)
            return True
        else:
            print(f"❌ Failed to send test message: {error}")
            await self.log_result(self.test_user_id, False, error)
            return False
    
    def get_user_confirmation(self, question):
        """Get user confirmation from terminal input"""
        while True:
            response = input(f"\n{question} (y/n): ").strip().lower()
            if response in ['y', 'yes']:
                return True
            elif response in ['n', 'no']:
                return False
            else:
                print("Please enter 'y' for yes or 'n' for no.")
    
    async def broadcast_to_all_users(self):
        """Send broadcast message to all users in batches"""
        print(f"\n🚀 STARTING FULL BROADCAST")
        print("=" * 50)
        
        # Load users and caption
        user_ids = await self.load_users()
        if not user_ids:
            print("❌ No users to broadcast to!")
            return
        
        caption = await self.load_caption()
        if not caption:
            print("❌ Failed to load caption!")
            return
        
        # Check if image exists
        if not os.path.exists(self.image_path):
            print(f"❌ Image file not found: {self.image_path}")
            return
        
        total_users = len(user_ids)
        successful_sends = 0
        failed_sends = 0
        
        print(f"📊 Total users to broadcast to: {total_users}")
        print(f"📦 Batch size: {self.batch_size} users per second")
        print(f"⏱️  Estimated time: {total_users / self.batch_size:.1f} seconds")
        print()
        
        # Process users in batches
        for i in range(0, total_users, self.batch_size):
            batch = user_ids[i:i + self.batch_size]
            batch_num = (i // self.batch_size) + 1
            total_batches = (total_users + self.batch_size - 1) // self.batch_size
            
            print(f"📤 Processing batch {batch_num}/{total_batches} ({len(batch)} users)...")
            
            # Send messages to all users in current batch concurrently
            tasks = []
            for user_id in batch:
                task = self.send_message_to_user(user_id, caption, self.image_path)
                tasks.append((user_id, task))
            
            # Wait for all tasks in batch to complete
            for user_id, task in tasks:
                success, error = await task
                await self.log_result(user_id, success, error)
                
                if success:
                    successful_sends += 1
                else:
                    failed_sends += 1
            
            # Progress update
            progress = ((i + len(batch)) / total_users) * 100
            print(f"✅ Batch {batch_num} completed. Progress: {progress:.1f}% "
                  f"(Success: {successful_sends}, Failed: {failed_sends})")
            
            # Wait before next batch (except for the last batch)
            if i + self.batch_size < total_users:
                await asyncio.sleep(self.delay_between_batches)
        
        print(f"\n🎉 BROADCAST COMPLETED!")
        print("=" * 50)
        print(f"📊 Final Results:")
        print(f"   ✅ Successful sends: {successful_sends}")
        print(f"   ❌ Failed sends: {failed_sends}")
        print(f"   📈 Success rate: {(successful_sends/total_users)*100:.1f}%")
        print(f"   📝 Detailed log saved to: {self.log_file}")

async def main():
    print("🤖 TELEGRAM BROADCAST SCRIPT")
    print("=" * 50)
    
    # Get bot token
    bot_token = input("Please enter your bot token: ").strip()
    if not bot_token:
        print("❌ Bot token is required!")
        return
    
    # Initialize broadcast bot
    broadcast_bot = BroadcastBot(bot_token)
    
    try:
        # Test bot connection
        print("\n🔗 Testing bot connection...")
        bot_info = await broadcast_bot.bot.get_me()
        print(f"✅ Connected to bot: @{bot_info.username}")

        # Check webhook status
        print("\n🔍 Checking for potential conflicts...")
        webhook_active = await broadcast_bot.check_webhook_status()

        if webhook_active:
            print("\n⚠️  IMPORTANT WARNINGS:")
            print("   1. Your main bot webhook will be DISABLED during broadcast")
            print("   2. Your main bot will NOT receive messages during broadcast")
            print("   3. You'll need to restart your main bot after broadcast")
            print("   4. Consider using a separate bot token for broadcasting")

            if not broadcast_bot.get_user_confirmation("Do you want to continue anyway?"):
                print("❌ Broadcast cancelled for safety.")
                return
        
        # Send test message
        test_success = await broadcast_bot.send_test_message()
        
        if not test_success:
            print("\n❌ Test message failed. Please check the error and try again.")
            return
        
        # Get confirmation about test message
        if not broadcast_bot.get_user_confirmation("Did you receive the test message?"):
            print("❌ Please check your bot setup and try again.")
            return
        
        if not broadcast_bot.get_user_confirmation("Does the post look good?"):
            print("❌ Please update your broadcast content and try again.")
            return
        
        # Final confirmation for full broadcast
        print(f"\n⚠️  FINAL CONFIRMATION")
        print("=" * 30)
        user_ids = await broadcast_bot.load_users()
        print(f"You are about to send the broadcast to {len(user_ids)} users.")
        
        if not broadcast_bot.get_user_confirmation("Do you want to proceed with the full broadcast?"):
            print("❌ Broadcast cancelled by user.")
            return
        
        # Start full broadcast
        await broadcast_bot.broadcast_to_all_users()
        
    except KeyboardInterrupt:
        print("\n❌ Broadcast interrupted by user.")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        print(f"❌ An error occurred: {e}")

if __name__ == "__main__":
    # Check if required files exist
    required_files = ["users.txt", "broadcast content/image.jpg", "broadcast content/media caption.txt"]
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print("❌ Missing required files:")
        for file in missing_files:
            print(f"   - {file}")
        print("\nPlease ensure all files are present before running the script.")
        sys.exit(1)
    
    # Run the broadcast script
    asyncio.run(main())
