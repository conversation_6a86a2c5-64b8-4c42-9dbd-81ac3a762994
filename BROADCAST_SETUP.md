# Telegram Broadcast Script Setup Guide

## Overview
This is a standalone broadcast script that operates completely independently from your existing bot infrastructure. It's designed to safely send broadcast messages without interfering with your current webhook-based bot.

## Files Created
- `broadcast_script.py` - Main broadcast script
- `requirements.txt` - Python dependencies
- `BROADCAST_SETUP.md` - This setup guide

## Prerequisites
- Python 3.7 or higher
- Your Telegram bot token
- Existing files:
  - `users.txt` - Contains comma-separated user IDs
  - `broadcast content/image.jpg` - Image to broadcast
  - `broadcast content/media caption.txt` - Caption text

## Installation

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Verify required files exist:**
   - ✅ `users.txt`
   - ✅ `broadcast content/image.jpg`
   - ✅ `broadcast content/media caption.txt`

## Usage

1. **Run the script:**
   ```bash
   python broadcast_script.py
   ```

2. **Follow the interactive prompts:**
   - Enter your bot token when prompted
   - The script will test the connection
   - A test message will be sent to user ID `**********`
   - Confirm you received the test message
   - Confirm the post looks good
   - Give final approval for the full broadcast

## Features

### Safety Features
- **Test First**: Always sends a test message before the full broadcast
- **User Confirmation**: Multiple confirmation steps to prevent accidental broadcasts
- **Separate from Main Bot**: Completely independent from your existing bot infrastructure
- **Error Handling**: Robust error handling for rate limits, blocked users, etc.

### Performance Features
- **Batch Processing**: Sends to 30 users per second for optimal performance
- **Rate Limit Handling**: Automatically handles Telegram rate limits
- **Concurrent Sending**: Processes batches concurrently for efficiency

### Logging Features
- **Detailed Logging**: Creates timestamped log files for each broadcast
- **Real-time Progress**: Shows progress updates during broadcast
- **Success/Failure Tracking**: Tracks which users received messages successfully

## Broadcast Process

1. **Test Phase:**
   - Sends test message to user `**********`
   - Waits for your confirmation

2. **Confirmation Phase:**
   - Asks if you received the test message
   - Asks if the post looks good
   - Asks for final approval to proceed

3. **Broadcast Phase:**
   - Processes users in batches of 30 per second
   - Shows real-time progress updates
   - Logs all results to a timestamped file

## Log Files

The script creates two types of logs:
- `broadcast.log` - General script activity
- `broadcast_log_YYYYMMDD_HHMMSS.txt` - Detailed per-user results

## Error Handling

The script handles common issues:
- **Blocked Users**: Logs when users have blocked the bot
- **Rate Limits**: Automatically waits and retries
- **Network Issues**: Retries failed sends
- **Invalid Users**: Logs users that can't receive messages

## Safety Notes

- The script is completely separate from your existing bot
- It won't interfere with your webhook-based bot
- All operations are logged for audit purposes
- Multiple confirmation steps prevent accidental broadcasts
- Test message ensures everything works before full broadcast

## Troubleshooting

**If the test message fails:**
- Check your bot token
- Ensure the test user ID `**********` can receive messages
- Verify the image and caption files exist

**If the broadcast is slow:**
- The script is designed for 30 users/second to respect Telegram limits
- This is optimal for avoiding rate limits

**If some users don't receive messages:**
- Check the log file for specific error reasons
- Common reasons: user blocked bot, user deleted account, invalid user ID
