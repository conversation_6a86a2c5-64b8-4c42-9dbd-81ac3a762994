#!/usr/bin/env python3
"""
User List Cleanup Script
This script checks which users can receive messages and removes blocked/invalid users from users.txt
"""

import asyncio
import logging
import os
import sys
from datetime import datetime
from pathlib import Path
import aiofiles
from telegram import Bo<PERSON>
from telegram.error import TelegramError, RetryAfter, Forbidden, BadRequest

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('cleanup.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class UserCleanup:
    def __init__(self, bot_token):
        self.bot = Bot(token=bot_token)
        self.users_file = "users.txt"
        self.backup_file = f"users_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        self.cleanup_log = f"cleanup_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        self.batch_size = 20  # Smaller batches for checking
        self.delay_between_batches = 2.0  # Longer delay for safety
        
    async def load_users(self):
        """Load user IDs from users.txt file"""
        try:
            async with aiofiles.open(self.users_file, 'r') as f:
                content = await f.read()
                user_ids = [int(uid.strip()) for uid in content.strip().split(',') if uid.strip()]
                logger.info(f"Loaded {len(user_ids)} user IDs from {self.users_file}")
                return user_ids
        except FileNotFoundError:
            logger.error(f"Users file {self.users_file} not found!")
            return []
        except Exception as e:
            logger.error(f"Error loading users: {e}")
            return []
    
    async def backup_users_file(self):
        """Create a backup of the original users.txt file"""
        try:
            async with aiofiles.open(self.users_file, 'r') as source:
                content = await source.read()
            
            async with aiofiles.open(self.backup_file, 'w') as backup:
                await backup.write(content)
            
            logger.info(f"Backup created: {self.backup_file}")
            return True
        except Exception as e:
            logger.error(f"Error creating backup: {e}")
            return False
    
    async def check_user_can_receive_message(self, user_id):
        """Check if a user can receive messages by sending a test message"""
        try:
            # Try to get chat info first (lightweight check)
            await self.bot.get_chat(user_id)
            
            # If that succeeds, try sending a very lightweight action
            await self.bot.send_chat_action(chat_id=user_id, action="typing")
            
            return True, "Active user"
            
        except Forbidden as e:
            error_msg = str(e).lower()
            if "blocked" in error_msg:
                return False, "User blocked the bot"
            elif "not found" in error_msg or "chat not found" in error_msg:
                return False, "User not found (deleted account)"
            elif "initiated" in error_msg:
                return False, "User hasn't started the bot"
            else:
                return False, f"Forbidden: {e}"
                
        except BadRequest as e:
            error_msg = str(e).lower()
            if "user not found" in error_msg or "chat not found" in error_msg:
                return False, "User not found (deleted account)"
            elif "deactivated" in error_msg:
                return False, "User account deactivated"
            else:
                return False, f"Bad request: {e}"
                
        except RetryAfter as e:
            logger.warning(f"Rate limited for {e.retry_after} seconds")
            await asyncio.sleep(e.retry_after)
            return await self.check_user_can_receive_message(user_id)
            
        except TelegramError as e:
            return False, f"Telegram error: {e}"
            
        except Exception as e:
            return False, f"Unexpected error: {e}"
    
    async def log_result(self, user_id, can_receive, reason):
        """Log the result of checking a user"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        status = "ACTIVE" if can_receive else "REMOVED"
        log_entry = f"{timestamp} - User {user_id}: {status} - {reason}\n"
        
        async with aiofiles.open(self.cleanup_log, 'a') as f:
            await f.write(log_entry)
    
    async def save_cleaned_users(self, active_users):
        """Save the cleaned user list back to users.txt"""
        try:
            # Convert to comma-separated string
            users_string = ','.join(map(str, active_users))
            
            async with aiofiles.open(self.users_file, 'w') as f:
                await f.write(users_string)
            
            logger.info(f"Cleaned users list saved to {self.users_file}")
            return True
        except Exception as e:
            logger.error(f"Error saving cleaned users: {e}")
            return False
    
    async def cleanup_users(self):
        """Main cleanup process"""
        print(f"\n🧹 STARTING USER LIST CLEANUP")
        print("=" * 50)
        
        # Load users
        user_ids = await self.load_users()
        if not user_ids:
            print("❌ No users to check!")
            return
        
        # Create backup
        if not await self.backup_users_file():
            print("❌ Failed to create backup!")
            return
        
        total_users = len(user_ids)
        active_users = []
        removed_users = []
        
        print(f"📊 Total users to check: {total_users}")
        print(f"📦 Batch size: {self.batch_size} users per batch")
        print(f"⏱️  Estimated time: {(total_users / self.batch_size) * self.delay_between_batches:.1f} seconds")
        print(f"💾 Backup created: {self.backup_file}")
        print()
        
        # Process users in batches
        for i in range(0, total_users, self.batch_size):
            batch = user_ids[i:i + self.batch_size]
            batch_num = (i // self.batch_size) + 1
            total_batches = (total_users + self.batch_size - 1) // self.batch_size
            
            print(f"🔍 Checking batch {batch_num}/{total_batches} ({len(batch)} users)...")
            
            # Check all users in current batch
            for user_id in batch:
                can_receive, reason = await self.check_user_can_receive_message(user_id)
                await self.log_result(user_id, can_receive, reason)
                
                if can_receive:
                    active_users.append(user_id)
                else:
                    removed_users.append((user_id, reason))
                
                # Small delay between individual checks
                await asyncio.sleep(0.1)
            
            # Progress update
            progress = ((i + len(batch)) / total_users) * 100
            print(f"✅ Batch {batch_num} completed. Progress: {progress:.1f}% "
                  f"(Active: {len(active_users)}, Removed: {len(removed_users)})")
            
            # Wait before next batch
            if i + self.batch_size < total_users:
                await asyncio.sleep(self.delay_between_batches)
        
        # Save cleaned users list
        if await self.save_cleaned_users(active_users):
            print(f"\n🎉 CLEANUP COMPLETED!")
            print("=" * 50)
            print(f"📊 Results Summary:")
            print(f"   📈 Original users: {total_users}")
            print(f"   ✅ Active users: {len(active_users)}")
            print(f"   ❌ Removed users: {len(removed_users)}")
            print(f"   📉 Removal rate: {(len(removed_users)/total_users)*100:.1f}%")
            print(f"   💾 Backup saved as: {self.backup_file}")
            print(f"   📝 Detailed log: {self.cleanup_log}")
            
            if removed_users:
                print(f"\n🗑️  Removed Users Breakdown:")
                reason_counts = {}
                for _, reason in removed_users:
                    reason_counts[reason] = reason_counts.get(reason, 0) + 1
                
                for reason, count in reason_counts.items():
                    print(f"   • {reason}: {count} users")
        else:
            print("❌ Failed to save cleaned users list!")

async def main():
    print("🧹 TELEGRAM USER LIST CLEANUP SCRIPT")
    print("=" * 50)
    print("This script will:")
    print("• Check which users can receive messages")
    print("• Remove blocked/invalid users from users.txt")
    print("• Create a backup of your original file")
    print("• Provide detailed logs of all changes")
    print()
    
    # Get bot token
    bot_token = input("Please enter your bot token: ").strip()
    if not bot_token:
        print("❌ Bot token is required!")
        return
    
    # Initialize cleanup
    cleanup = UserCleanup(bot_token)
    
    try:
        # Test bot connection
        print("\n🔗 Testing bot connection...")
        bot_info = await cleanup.bot.get_me()
        print(f"✅ Connected to bot: @{bot_info.username}")
        
        # Warning about webhook
        print(f"\n⚠️  WARNING: This script uses polling and will temporarily disable")
        print(f"   any active webhooks for this bot token!")
        
        # Confirmation
        response = input("\nDo you want to proceed with the cleanup? (y/n): ").strip().lower()
        if response not in ['y', 'yes']:
            print("❌ Cleanup cancelled by user.")
            return
        
        # Start cleanup
        await cleanup.cleanup_users()
        
    except KeyboardInterrupt:
        print("\n❌ Cleanup interrupted by user.")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        print(f"❌ An error occurred: {e}")

if __name__ == "__main__":
    # Check if users.txt exists
    if not os.path.exists("users.txt"):
        print("❌ users.txt file not found!")
        print("Please ensure users.txt exists in the current directory.")
        sys.exit(1)
    
    # Run the cleanup script
    asyncio.run(main())
